package com.cht.autowallet.data;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

/**
 * Room database for AutoWallet app
 */
@Database(
    entities = {Transaction.class, NotificationLog.class},
    version = 2,
    exportSchema = false
)
public abstract class AutoWalletDatabase extends RoomDatabase {

    private static final String DATABASE_NAME = "autowallet_database";
    private static volatile AutoWalletDatabase INSTANCE;

    public abstract TransactionDao transactionDao();
    public abstract NotificationLogDao notificationLogDao();
    
    public static AutoWalletDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AutoWalletDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            AutoWalletDatabase.class,
                            DATABASE_NAME
                    ).fallbackToDestructiveMigration() // 简单起见，使用破坏性迁移
                    .build();
                }
            }
        }
        return INSTANCE;
    }
    
    public static void destroyInstance() {
        INSTANCE = null;
    }
}
