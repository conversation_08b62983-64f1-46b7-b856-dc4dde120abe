package com.cht.autowallet.data;

import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

/**
 * 通知拦截日志实体
 */
@Entity(tableName = "notification_logs")
public class NotificationLog {
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    private String packageName;        // 通知来源应用包名
    private String appName;           // 应用显示名称
    private String title;             // 通知标题
    private String content;           // 通知内容
    private String bigText;           // 通知大文本内容
    private long timestamp;           // 拦截时间戳
    private boolean parseSuccess;     // 是否解析成功
    private String parseResult;       // 解析结果描述
    
    // 构造函数
    public NotificationLog() {}
    
    @Ignore
    public NotificationLog(String packageName, String appName, String title, 
                          String content, String bigText, long timestamp, 
                          boolean parseSuccess, String parseResult) {
        this.packageName = packageName;
        this.appName = appName;
        this.title = title;
        this.content = content;
        this.bigText = bigText;
        this.timestamp = timestamp;
        this.parseSuccess = parseSuccess;
        this.parseResult = parseResult;
    }
    
    // Getters and Setters
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public String getPackageName() { return packageName; }
    public void setPackageName(String packageName) { this.packageName = packageName; }
    
    public String getAppName() { return appName; }
    public void setAppName(String appName) { this.appName = appName; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public String getBigText() { return bigText; }
    public void setBigText(String bigText) { this.bigText = bigText; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public boolean isParseSuccess() { return parseSuccess; }
    public void setParseSuccess(boolean parseSuccess) { this.parseSuccess = parseSuccess; }
    
    public String getParseResult() { return parseResult; }
    public void setParseResult(String parseResult) { this.parseResult = parseResult; }
    
    /**
     * 获取完整的通知文本内容
     */
    public String getFullText() {
        StringBuilder sb = new StringBuilder();
        if (title != null && !title.isEmpty()) {
            sb.append(title).append(" ");
        }
        if (content != null && !content.isEmpty()) {
            sb.append(content).append(" ");
        }
        if (bigText != null && !bigText.isEmpty()) {
            sb.append(bigText);
        }
        return sb.toString().trim();
    }
    
    @Override
    public String toString() {
        return "NotificationLog{" +
                "id=" + id +
                ", packageName='" + packageName + '\'' +
                ", appName='" + appName + '\'' +
                ", title='" + title + '\'' +
                ", parseSuccess=" + parseSuccess +
                ", timestamp=" + timestamp +
                '}';
    }
}
