package com.cht.autowallet.data;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * Data Access Object for Transaction entity
 */
@Dao
public interface TransactionDao {
    
    @Insert
    long insert(Transaction transaction);
    
    @Update
    void update(Transaction transaction);
    
    @Delete
    void delete(Transaction transaction);
    
    @Query("SELECT * FROM transactions ORDER BY timestamp DESC")
    LiveData<List<Transaction>> getAllTransactions();
    
    @Query("SELECT * FROM transactions WHERE id = :id")
    LiveData<Transaction> getTransactionById(long id);
    
    @Query("SELECT * FROM transactions WHERE appPackage = :appPackage ORDER BY timestamp DESC")
    LiveData<List<Transaction>> getTransactionsByApp(String appPackage);
    
    @Query("SELECT * FROM transactions WHERE transactionType = :type ORDER BY timestamp DESC")
    LiveData<List<Transaction>> getTransactionsByType(String type);
    
    @Query("SELECT * FROM transactions WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    LiveData<List<Transaction>> getTransactionsByDateRange(long startTime, long endTime);
    
    @Query("SELECT * FROM transactions WHERE category = :category ORDER BY timestamp DESC")
    LiveData<List<Transaction>> getTransactionsByCategory(String category);
    
    @Query("SELECT SUM(amount) FROM transactions WHERE transactionType = 'expense' AND timestamp BETWEEN :startTime AND :endTime")
    LiveData<Double> getTotalExpenseInRange(long startTime, long endTime);
    
    @Query("SELECT SUM(amount) FROM transactions WHERE transactionType = 'income' AND timestamp BETWEEN :startTime AND :endTime")
    LiveData<Double> getTotalIncomeInRange(long startTime, long endTime);
    
    @Query("DELETE FROM transactions")
    void deleteAllTransactions();
    
    @Query("SELECT COUNT(*) FROM transactions")
    LiveData<Integer> getTransactionCount();
    
    // For debugging - get recent transactions without LiveData
    @Query("SELECT * FROM transactions ORDER BY timestamp DESC LIMIT :limit")
    List<Transaction> getRecentTransactions(int limit);
}
