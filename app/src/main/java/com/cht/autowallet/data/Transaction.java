package com.cht.autowallet.data;

import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

/**
 * Transaction entity representing a parsed payment transaction
 */
@Entity(tableName = "transactions")
public class Transaction {
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    private String appName;           // Source app (Alipay, WeChat Pay, etc.)
    private String appPackage;        // Package name of source app
    private String transactionType;   // "income" or "expense"
    private double amount;            // Transaction amount
    private String currency;          // Currency (CNY, USD, etc.)
    private String merchant;          // Merchant/recipient name
    private String description;       // Transaction description
    private long timestamp;           // Transaction timestamp
    private String rawNotificationText; // Original notification text for debugging
    private String category;          // Transaction category (food, transport, etc.)
    
    // Constructors
    public Transaction() {}

    @Ignore
    public Transaction(String appName, String appPackage, String transactionType,
                      double amount, String currency, String merchant, String description,
                      long timestamp, String rawNotificationText) {
        this.appName = appName;
        this.appPackage = appPackage;
        this.transactionType = transactionType;
        this.amount = amount;
        this.currency = currency;
        this.merchant = merchant;
        this.description = description;
        this.timestamp = timestamp;
        this.rawNotificationText = rawNotificationText;
        this.category = "未分类"; // Default category: "Uncategorized"
    }
    
    // Getters and Setters
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public String getAppName() { return appName; }
    public void setAppName(String appName) { this.appName = appName; }
    
    public String getAppPackage() { return appPackage; }
    public void setAppPackage(String appPackage) { this.appPackage = appPackage; }
    
    public String getTransactionType() { return transactionType; }
    public void setTransactionType(String transactionType) { this.transactionType = transactionType; }
    
    public double getAmount() { return amount; }
    public void setAmount(double amount) { this.amount = amount; }
    
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
    
    public String getMerchant() { return merchant; }
    public void setMerchant(String merchant) { this.merchant = merchant; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public String getRawNotificationText() { return rawNotificationText; }
    public void setRawNotificationText(String rawNotificationText) { this.rawNotificationText = rawNotificationText; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    @Override
    public String toString() {
        return "Transaction{" +
                "id=" + id +
                ", appName='" + appName + '\'' +
                ", transactionType='" + transactionType + '\'' +
                ", amount=" + amount +
                ", currency='" + currency + '\'' +
                ", merchant='" + merchant + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
