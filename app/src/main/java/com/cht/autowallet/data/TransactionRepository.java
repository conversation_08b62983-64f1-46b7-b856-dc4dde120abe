package com.cht.autowallet.data;

import android.content.Context;
import android.os.AsyncTask;

import androidx.lifecycle.LiveData;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Repository class to handle data operations
 */
public class TransactionRepository {
    
    private TransactionDao transactionDao;
    private LiveData<List<Transaction>> allTransactions;
    private ExecutorService executor;
    
    public TransactionRepository(Context context) {
        AutoWalletDatabase database = AutoWalletDatabase.getInstance(context);
        transactionDao = database.transactionDao();
        allTransactions = transactionDao.getAllTransactions();
        executor = Executors.newFixedThreadPool(2);
    }
    
    public LiveData<List<Transaction>> getAllTransactions() {
        return allTransactions;
    }
    
    public LiveData<List<Transaction>> getTransactionsByApp(String appPackage) {
        return transactionDao.getTransactionsByApp(appPackage);
    }
    
    public LiveData<List<Transaction>> getTransactionsByType(String type) {
        return transactionDao.getTransactionsByType(type);
    }
    
    public LiveData<List<Transaction>> getTransactionsByDateRange(long startTime, long endTime) {
        return transactionDao.getTransactionsByDateRange(startTime, endTime);
    }
    
    public LiveData<Integer> getTransactionCount() {
        return transactionDao.getTransactionCount();
    }
    
    public void insert(Transaction transaction) {
        executor.execute(() -> transactionDao.insert(transaction));
    }

    public long insertSync(Transaction transaction) {
        return transactionDao.insert(transaction);
    }
    
    public void update(Transaction transaction) {
        executor.execute(() -> transactionDao.update(transaction));
    }
    
    public void delete(Transaction transaction) {
        executor.execute(() -> transactionDao.delete(transaction));
    }
    
    public void deleteAllTransactions() {
        executor.execute(() -> transactionDao.deleteAllTransactions());
    }
    
    // For debugging purposes
    public void insertTransactionSync(Transaction transaction) {
        new InsertAsyncTask(transactionDao).execute(transaction);
    }
    
    private static class InsertAsyncTask extends AsyncTask<Transaction, Void, Void> {
        private TransactionDao transactionDao;
        
        InsertAsyncTask(TransactionDao dao) {
            transactionDao = dao;
        }
        
        @Override
        protected Void doInBackground(Transaction... transactions) {
            transactionDao.insert(transactions[0]);
            return null;
        }
    }
}
