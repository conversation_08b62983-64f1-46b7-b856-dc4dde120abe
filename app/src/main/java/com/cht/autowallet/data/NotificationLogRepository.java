package com.cht.autowallet.data;

import android.content.Context;

import androidx.lifecycle.LiveData;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 通知日志数据仓库
 */
public class NotificationLogRepository {
    
    private NotificationLogDao notificationLogDao;
    private LiveData<List<NotificationLog>> allLogs;
    private ExecutorService executor;
    
    public NotificationLogRepository(Context context) {
        AutoWalletDatabase database = AutoWalletDatabase.getInstance(context);
        notificationLogDao = database.notificationLogDao();
        allLogs = notificationLogDao.getAllLogs();
        executor = Executors.newFixedThreadPool(2);
    }
    
    public LiveData<List<NotificationLog>> getAllLogs() {
        return allLogs;
    }
    
    public LiveData<List<NotificationLog>> getLogsByPackage(String packageName) {
        return notificationLogDao.getLogsByPackage(packageName);
    }
    
    public LiveData<List<NotificationLog>> getLogsByParseStatus(boolean parseSuccess) {
        return notificationLogDao.getLogsByParseStatus(parseSuccess);
    }
    
    public LiveData<Integer> getLogCount() {
        return notificationLogDao.getLogCount();
    }
    
    public LiveData<Integer> getSuccessfulParseCount() {
        return notificationLogDao.getSuccessfulParseCount();
    }
    
    public void insert(NotificationLog log) {
        executor.execute(() -> notificationLogDao.insert(log));
    }
    
    public void delete(NotificationLog log) {
        executor.execute(() -> notificationLogDao.delete(log));
    }
    
    public void deleteAllLogs() {
        executor.execute(() -> notificationLogDao.deleteAllLogs());
    }
    
    public void deleteOldLogs(long beforeTime) {
        executor.execute(() -> notificationLogDao.deleteLogsBefore(beforeTime));
    }
    
    // 清理超过指定天数的日志
    public void cleanupOldLogs(int daysToKeep) {
        long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60L * 60L * 1000L);
        deleteOldLogs(cutoffTime);
    }
}
