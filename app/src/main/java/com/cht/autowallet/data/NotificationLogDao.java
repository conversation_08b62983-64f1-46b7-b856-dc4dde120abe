package com.cht.autowallet.data;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;

import java.util.List;

/**
 * 通知日志数据访问对象
 */
@Dao
public interface NotificationLogDao {
    
    @Insert
    long insert(NotificationLog log);
    
    @Delete
    void delete(NotificationLog log);
    
    @Query("SELECT * FROM notification_logs ORDER BY timestamp DESC")
    LiveData<List<NotificationLog>> getAllLogs();
    
    @Query("SELECT * FROM notification_logs WHERE packageName = :packageName ORDER BY timestamp DESC")
    LiveData<List<NotificationLog>> getLogsByPackage(String packageName);
    
    @Query("SELECT * FROM notification_logs WHERE parseSuccess = :parseSuccess ORDER BY timestamp DESC")
    LiveData<List<NotificationLog>> getLogsByParseStatus(boolean parseSuccess);
    
    @Query("SELECT * FROM notification_logs WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    LiveData<List<NotificationLog>> getLogsByTimeRange(long startTime, long endTime);
    
    @Query("DELETE FROM notification_logs")
    void deleteAllLogs();
    
    @Query("DELETE FROM notification_logs WHERE timestamp < :beforeTime")
    void deleteLogsBefore(long beforeTime);
    
    @Query("SELECT COUNT(*) FROM notification_logs")
    LiveData<Integer> getLogCount();
    
    @Query("SELECT COUNT(*) FROM notification_logs WHERE parseSuccess = 1")
    LiveData<Integer> getSuccessfulParseCount();
    
    // 获取最近的日志（用于调试，不使用LiveData）
    @Query("SELECT * FROM notification_logs ORDER BY timestamp DESC LIMIT :limit")
    List<NotificationLog> getRecentLogs(int limit);
}
