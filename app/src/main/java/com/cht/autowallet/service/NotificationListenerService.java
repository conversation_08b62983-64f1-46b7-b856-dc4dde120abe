package com.cht.autowallet.service;

import android.app.Notification;
import android.content.Intent;
import android.os.IBinder;
import android.service.notification.StatusBarNotification;
import android.util.Log;

import com.cht.autowallet.data.NotificationLog;
import com.cht.autowallet.data.NotificationLogRepository;
import com.cht.autowallet.data.Transaction;
import com.cht.autowallet.data.TransactionRepository;
import com.cht.autowallet.parser.NotificationParser;
import com.cht.autowallet.parser.NotificationParserFactory;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Service to listen for notifications from payment apps and parse transaction data
 */
public class NotificationListenerService extends android.service.notification.NotificationListenerService {
    
    private static final String TAG = "AutoWallet_NLS";
    
    // Target payment app packages
    private static final Set<String> TARGET_PACKAGES = new HashSet<>(Arrays.asList(
        "com.eg.android.AlipayGphone",     // Alipay
        "com.tencent.mm",                  // WeChat
        "com.icbc",                        // ICBC (工商银行)
        "com.ccb.ccbnetpay",              // CCB (建设银行)
        "com.bankcomm.Bankcomm",          // Bank of Communications (交通银行)
        "cmb.pb",                         // China Merchants Bank (招商银行)
        "com.chinamworld.bocmbci",        // Bank of China (中国银行)
        "com.abc.mobile.android",         // Agricultural Bank of China (农业银行)
        "com.spdb.mobilebank.per",        // Shanghai Pudong Development Bank (浦发银行)
        "com.citic.bank.mobile",          // CITIC Bank (中信银行)
        "com.pingan.paces.ccms",          // Ping An Bank (平安银行)
        "com.cmbchina.ccd.pluto.cmbActivity" // China Merchants Bank Credit Card
    ));
    
    private TransactionRepository repository;
    private NotificationLogRepository logRepository;
    private NotificationParserFactory parserFactory;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "NotificationListenerService created");
        repository = new TransactionRepository(this);
        logRepository = new NotificationLogRepository(this);
        parserFactory = new NotificationParserFactory();
    }
    
    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        super.onNotificationPosted(sbn);

        String packageName = sbn.getPackageName();

        // 记录所有目标应用的通知（包括非支付通知）
        if (TARGET_PACKAGES.contains(packageName)) {
            Log.d(TAG, "Received notification from: " + packageName);

            Notification notification = sbn.getNotification();
            if (notification == null) {
                Log.w(TAG, "Notification is null");
                return;
            }

            // Extract notification text
            String title = "";
            String text = "";
            String bigText = "";

            if (notification.extras != null) {
                CharSequence titleSeq = notification.extras.getCharSequence(Notification.EXTRA_TITLE);
                CharSequence textSeq = notification.extras.getCharSequence(Notification.EXTRA_TEXT);
                CharSequence bigTextSeq = notification.extras.getCharSequence(Notification.EXTRA_BIG_TEXT);

                title = titleSeq != null ? titleSeq.toString() : "";
                text = textSeq != null ? textSeq.toString() : "";
                bigText = bigTextSeq != null ? bigTextSeq.toString() : "";
            }

            // Combine all text for parsing
            String fullText = title + " " + text + " " + bigText;

            Log.d(TAG, "Notification text: " + fullText);

            // 记录通知日志并尝试解析
            logAndParseNotification(packageName, title, text, bigText, sbn.getPostTime());
        }
    }
    
    @Override
    public void onNotificationRemoved(StatusBarNotification sbn) {
        super.onNotificationRemoved(sbn);
        // We don't need to do anything when notifications are removed
    }
    
    private void logAndParseNotification(String packageName, String title, String text, String bigText, long timestamp) {
        String appName = getAppDisplayName(packageName);
        String fullText = (title + " " + text + " " + bigText).trim();

        boolean parseSuccess = false;
        String parseResult = "未解析";

        try {
            NotificationParser parser = parserFactory.getParser(packageName);
            if (parser != null) {
                Transaction transaction = parser.parseNotification(packageName, fullText, timestamp);
                if (transaction != null) {
                    Log.d(TAG, "Parsed transaction: " + transaction.toString());

                    // 在后台线程中插入数据库并发送广播
                    new Thread(() -> {
                        try {
                            // 同步插入并获取生成的ID
                            long transactionId = repository.insertSync(transaction);
                            transaction.setId(transactionId);

                            Log.d(TAG, "Transaction inserted with ID: " + transactionId);

                            // Broadcast that a new transaction was added
                            Intent intent = new Intent("com.cht.autowallet.NEW_TRANSACTION");
                            intent.setPackage(getPackageName());
                            intent.putExtra("transaction_id", transactionId);
                            sendBroadcast(intent);
                        } catch (Exception e) {
                            Log.e(TAG, "Error inserting transaction", e);
                        }
                    }).start();

                    parseSuccess = true;
                    parseResult = "解析成功 - " + transaction.getTransactionType() + " " + transaction.getAmount() + "元";
                } else {
                    parseResult = "解析失败 - 未识别为交易通知";
                }
            } else {
                parseResult = "无可用解析器";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing notification", e);
            parseResult = "解析异常: " + e.getMessage();
        }

        // 记录通知日志
        NotificationLog log = new NotificationLog(
            packageName, appName, title, text, bigText,
            timestamp, parseSuccess, parseResult
        );
        logRepository.insert(log);

        Log.d(TAG, "Logged notification: " + parseResult);
    }

    private String getAppDisplayName(String packageName) {
        switch (packageName) {
            case "com.eg.android.AlipayGphone": return "支付宝";
            case "com.tencent.mm": return "微信";
            case "com.icbc": return "工商银行";
            case "com.ccb.ccbnetpay": return "建设银行";
            case "com.bankcomm.Bankcomm": return "交通银行";
            case "cmb.pb": return "招商银行";
            case "com.chinamworld.bocmbci": return "中国银行";
            case "com.abc.mobile.android": return "农业银行";
            case "com.spdb.mobilebank.per": return "浦发银行";
            case "com.citic.bank.mobile": return "中信银行";
            case "com.pingan.paces.ccms": return "平安银行";
            case "com.cmbchina.ccd.pluto.cmbActivity": return "招商银行信用卡";
            default: return packageName;
        }
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return super.onBind(intent);
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "NotificationListenerService destroyed");
    }
}
