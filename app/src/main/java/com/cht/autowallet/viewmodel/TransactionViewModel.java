package com.cht.autowallet.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.cht.autowallet.data.Transaction;
import com.cht.autowallet.data.TransactionRepository;

import java.util.List;

/**
 * ViewModel for managing transaction data
 */
public class TransactionViewModel extends AndroidViewModel {
    
    private TransactionRepository repository;
    private LiveData<List<Transaction>> allTransactions;
    
    public TransactionViewModel(@NonNull Application application) {
        super(application);
        repository = new TransactionRepository(application);
        allTransactions = repository.getAllTransactions();
    }
    
    public LiveData<List<Transaction>> getAllTransactions() {
        return allTransactions;
    }
    
    public LiveData<List<Transaction>> getTransactionsByApp(String appPackage) {
        return repository.getTransactionsByApp(appPackage);
    }
    
    public LiveData<List<Transaction>> getTransactionsByType(String type) {
        return repository.getTransactionsByType(type);
    }
    
    public LiveData<List<Transaction>> getTransactionsByDateRange(long startTime, long endTime) {
        return repository.getTransactionsByDateRange(startTime, endTime);
    }
    
    public LiveData<Integer> getTransactionCount() {
        return repository.getTransactionCount();
    }
    
    public void insert(Transaction transaction) {
        repository.insert(transaction);
    }
    
    public void update(Transaction transaction) {
        repository.update(transaction);
    }
    
    public void delete(Transaction transaction) {
        repository.delete(transaction);
    }
    
    public void deleteAllTransactions() {
        repository.deleteAllTransactions();
    }
}
