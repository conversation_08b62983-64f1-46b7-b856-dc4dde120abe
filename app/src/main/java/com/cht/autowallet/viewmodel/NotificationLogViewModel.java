package com.cht.autowallet.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.cht.autowallet.data.NotificationLog;
import com.cht.autowallet.data.NotificationLogRepository;

import java.util.List;

/**
 * 通知日志ViewModel
 */
public class NotificationLogViewModel extends AndroidViewModel {
    
    private NotificationLogRepository repository;
    private LiveData<List<NotificationLog>> allLogs;
    
    public NotificationLogViewModel(@NonNull Application application) {
        super(application);
        repository = new NotificationLogRepository(application);
        allLogs = repository.getAllLogs();
    }
    
    public LiveData<List<NotificationLog>> getAllLogs() {
        return allLogs;
    }
    
    public LiveData<List<NotificationLog>> getLogsByPackage(String packageName) {
        return repository.getLogsByPackage(packageName);
    }
    
    public LiveData<List<NotificationLog>> getLogsByParseStatus(boolean parseSuccess) {
        return repository.getLogsByParseStatus(parseSuccess);
    }
    
    public LiveData<Integer> getLogCount() {
        return repository.getLogCount();
    }
    
    public LiveData<Integer> getSuccessfulParseCount() {
        return repository.getSuccessfulParseCount();
    }
    
    public void insert(NotificationLog log) {
        repository.insert(log);
    }
    
    public void delete(NotificationLog log) {
        repository.delete(log);
    }
    
    public void deleteAllLogs() {
        repository.deleteAllLogs();
    }
    
    public void cleanupOldLogs(int daysToKeep) {
        repository.cleanupOldLogs(daysToKeep);
    }
}
