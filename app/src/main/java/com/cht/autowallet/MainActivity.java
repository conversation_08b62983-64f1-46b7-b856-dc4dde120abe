package com.cht.autowallet;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.cht.autowallet.adapter.TransactionAdapter;
import com.cht.autowallet.data.NotificationLogRepository;
import com.cht.autowallet.data.Transaction;
import com.cht.autowallet.data.TransactionRepository;
import com.cht.autowallet.utils.ParserTestHelper;
import com.cht.autowallet.utils.PermissionManager;
import com.cht.autowallet.utils.TestDataGenerator;
import com.cht.autowallet.viewmodel.TransactionViewModel;

import java.util.ArrayList;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";

    private TextView statusText;
    private Button permissionButton;
    private Button batteryOptimizationButton;
    private Button testDataButton;
    private Button testParserButton;
    private Button viewLogsButton;
    private RecyclerView transactionRecyclerView;
    private TransactionAdapter transactionAdapter;
    private TransactionViewModel transactionViewModel;
    private TransactionRepository repository;
    private NotificationLogRepository logRepository;

    private BroadcastReceiver newTransactionReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.cht.autowallet.NEW_TRANSACTION".equals(intent.getAction())) {
                long transactionId = intent.getLongExtra("transaction_id", -1);
                Log.d(TAG, "Received new transaction broadcast, ID: " + transactionId);
                Toast.makeText(MainActivity.this, "新交易已记录 (ID: " + transactionId + ")", Toast.LENGTH_SHORT).show();
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initViews();
        setupRecyclerView();
        setupViewModel();
        setupRepository();
        checkPermissions();
    }

    private void initViews() {
        statusText = findViewById(R.id.statusText);
        permissionButton = findViewById(R.id.permissionButton);
        batteryOptimizationButton = findViewById(R.id.batteryOptimizationButton);
        testDataButton = findViewById(R.id.testDataButton);
        testParserButton = findViewById(R.id.testParserButton);
        viewLogsButton = findViewById(R.id.viewLogsButton);
        transactionRecyclerView = findViewById(R.id.transactionRecyclerView);

        permissionButton.setOnClickListener(v -> handlePermissionRequest());
        batteryOptimizationButton.setOnClickListener(v -> handleBatteryOptimizationRequest());
        testDataButton.setOnClickListener(v -> generateTestData());
        testParserButton.setOnClickListener(v -> testParsers());
        viewLogsButton.setOnClickListener(v -> openLogView());
    }

    private void setupRecyclerView() {
        transactionAdapter = new TransactionAdapter(new ArrayList<>());
        transactionRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        transactionRecyclerView.setAdapter(transactionAdapter);
    }

    private void setupViewModel() {
        transactionViewModel = new ViewModelProvider(this).get(TransactionViewModel.class);
        transactionViewModel.getAllTransactions().observe(this, transactions -> {
            if (transactions != null) {
                Log.d(TAG, "Received " + transactions.size() + " transactions from ViewModel");
                transactionAdapter.updateTransactions(transactions);
            }
        });
    }

    private void setupRepository() {
        repository = new TransactionRepository(this);
        logRepository = new NotificationLogRepository(this);
    }

    private void generateTestData() {
        TestDataGenerator.generateSampleTransactions(repository);
        TestDataGenerator.generateSampleNotificationLogs(logRepository);
        Toast.makeText(this, "测试数据已生成（包含交易记录和通知日志）", Toast.LENGTH_SHORT).show();
    }

    private void testParsers() {
        ParserTestHelper.runAllTests();
        ParserTestHelper.testFullWorkflow(this);
        Toast.makeText(this, "解析器测试已运行，请查看Logcat输出", Toast.LENGTH_LONG).show();
    }

    private void openLogView() {
        Intent intent = new Intent(this, LogViewActivity.class);
        startActivity(intent);
    }

    private void checkPermissions() {
        boolean notificationGranted = PermissionManager.isNotificationAccessGranted(this);
        boolean batteryOptimized = PermissionManager.isBatteryOptimizationIgnored(this);

        if (notificationGranted && batteryOptimized) {
            statusText.setText("所有权限已授予 - AutoWallet正在后台监听支付通知");
            permissionButton.setText("权限设置");
            batteryOptimizationButton.setText("电池优化设置");
        } else if (notificationGranted) {
            statusText.setText("通知权限已授予，建议关闭电池优化以确保后台运行");
            permissionButton.setText("权限设置");
            batteryOptimizationButton.setText("关闭电池优化");
        } else {
            statusText.setText("需要通知访问权限才能自动记录交易");
            permissionButton.setText("授予通知权限");
            batteryOptimizationButton.setText("关闭电池优化");
        }
    }

    private void handlePermissionRequest() {
        if (!PermissionManager.isNotificationAccessGranted(this)) {
            showPermissionDialog();
        } else {
            PermissionManager.openNotificationAccessSettings(this);
        }
    }

    private void showPermissionDialog() {
        new AlertDialog.Builder(this)
                .setTitle("需要通知访问权限")
                .setMessage("AutoWallet需要访问通知权限来自动识别支付应用的交易通知。\n\n" +
                           "请在设置中找到\"AutoWallet\"并开启通知访问权限。")
                .setPositiveButton("去设置", (dialog, which) -> {
                    PermissionManager.openNotificationAccessSettings(this);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void handleBatteryOptimizationRequest() {
        if (!PermissionManager.isBatteryOptimizationIgnored(this)) {
            showBatteryOptimizationDialog();
        } else {
            PermissionManager.openBatteryOptimizationSettings(this);
        }
    }

    private void showBatteryOptimizationDialog() {
        new AlertDialog.Builder(this)
                .setTitle("关闭电池优化")
                .setMessage("为了确保AutoWallet能够在后台持续监听通知，建议将应用加入电池优化白名单。\n\n" +
                           "这样可以防止系统自动关闭通知监听服务。")
                .setPositiveButton("去设置", (dialog, which) -> {
                    PermissionManager.requestIgnoreBatteryOptimization(this);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        checkPermissions();

        // Register receiver for new transactions
        IntentFilter filter = new IntentFilter("com.cht.autowallet.NEW_TRANSACTION");
        ContextCompat.registerReceiver(this, newTransactionReceiver, filter, ContextCompat.RECEIVER_NOT_EXPORTED);
    }

    @Override
    protected void onPause() {
        super.onPause();
        try {
            unregisterReceiver(newTransactionReceiver);
        } catch (IllegalArgumentException e) {
            // Receiver was not registered
        }
    }
}