package com.cht.autowallet.parser;

import android.util.Log;

import com.cht.autowallet.data.Transaction;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Generic parser for Chinese bank notifications
 * Handles various bank notification formats for transactions
 */
public class BankParser implements NotificationParser {
    
    private static final String TAG = "BankParser";
    
    // Map of bank packages to their display names
    private static final Map<String, String> BANK_NAMES = new HashMap<>();
    static {
        BANK_NAMES.put("com.icbc", "工商银行");
        BANK_NAMES.put("com.ccb.ccbnetpay", "建设银行");
        BANK_NAMES.put("com.bankcomm.Bankcomm", "交通银行");
        BANK_NAMES.put("cmb.pb", "招商银行");
        BANK_NAMES.put("com.chinamworld.bocmbci", "中国银行");
        BANK_NAMES.put("com.abc.mobile.android", "农业银行");
        BANK_NAMES.put("com.spdb.mobilebank.per", "浦发银行");
        BANK_NAMES.put("com.citic.bank.mobile", "中信银行");
        BANK_NAMES.put("com.pingan.paces.ccms", "平安银行");
        BANK_NAMES.put("com.cmbchina.ccd.pluto.cmbActivity", "招商银行信用卡");
    }
    
    // Regex patterns for different bank notification formats
    private static final Pattern DEBIT_PATTERN = Pattern.compile(
        ".*?(支出|消费|扣款|付款).*?([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern CREDIT_PATTERN = Pattern.compile(
        ".*?(收入|入账|转入|存入).*?([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TRANSFER_OUT_PATTERN = Pattern.compile(
        ".*?(转出|转账).*?([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TRANSFER_IN_PATTERN = Pattern.compile(
        ".*?(转入|到账).*?([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern AMOUNT_PATTERN = Pattern.compile(
        "([0-9]+\\.?[0-9]*)[元¥]", Pattern.CASE_INSENSITIVE
    );
    
    // Pattern to extract merchant/location information
    private static final Pattern MERCHANT_PATTERN = Pattern.compile(
        ".*?在(.+?)(支出|消费|扣款|付款).*", Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public boolean canHandle(String packageName) {
        return BANK_NAMES.containsKey(packageName);
    }
    
    @Override
    public Transaction parseNotification(String packageName, String notificationText, long timestamp) {
        if (!canHandle(packageName)) {
            return null;
        }
        
        Log.d(TAG, "Parsing bank notification from " + packageName + ": " + notificationText);
        
        // Skip non-transaction notifications
        if (!isTransactionNotification(notificationText)) {
            return null;
        }
        
        // Try different patterns
        Transaction transaction = tryParseDebit(notificationText, timestamp);
        if (transaction == null) {
            transaction = tryParseCredit(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseTransferOut(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseTransferIn(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseGeneric(notificationText, timestamp);
        }
        
        if (transaction != null) {
            String bankName = BANK_NAMES.get(packageName);
            transaction.setAppName(bankName != null ? bankName : "银行");
            transaction.setAppPackage(packageName);
            transaction.setCurrency("CNY");
            transaction.setRawNotificationText(notificationText);
        }
        
        return transaction;
    }
    
    private boolean isTransactionNotification(String text) {
        return text.contains("支出") || text.contains("消费") || text.contains("扣款") ||
               text.contains("收入") || text.contains("入账") || text.contains("转入") ||
               text.contains("转出") || text.contains("转账") || text.contains("付款") ||
               text.contains("存入") || text.contains("到账") || text.contains("元");
    }
    
    private Transaction tryParseDebit(String text, long timestamp) {
        Matcher matcher = DEBIT_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                String merchant = extractMerchant(text);
                
                return new Transaction(
                    "", "", "expense", amount, "CNY",
                    merchant, "银行卡支出", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseCredit(String text, long timestamp) {
        Matcher matcher = CREDIT_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                return new Transaction(
                    "", "", "income", amount, "CNY",
                    "未知", "银行卡收入", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseTransferOut(String text, long timestamp) {
        Matcher matcher = TRANSFER_OUT_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                return new Transaction(
                    "", "", "expense", amount, "CNY",
                    "转账", "银行转账", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseTransferIn(String text, long timestamp) {
        Matcher matcher = TRANSFER_IN_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                return new Transaction(
                    "", "", "income", amount, "CNY",
                    "转账", "银行转账收入", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseGeneric(String text, long timestamp) {
        Matcher amountMatcher = AMOUNT_PATTERN.matcher(text);
        if (amountMatcher.find()) {
            String amountStr = amountMatcher.group(1);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                // Determine transaction type based on keywords
                String type = "expense"; // Default to expense
                String description = "银行交易";
                
                if (text.contains("收入") || text.contains("入账") || 
                    text.contains("转入") || text.contains("存入") || text.contains("到账")) {
                    type = "income";
                    description = "银行收入";
                }
                
                String merchant = extractMerchant(text);
                
                return new Transaction(
                    "", "", type, amount, "CNY",
                    merchant, description, timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        
        return null;
    }
    
    private String extractMerchant(String text) {
        Matcher merchantMatcher = MERCHANT_PATTERN.matcher(text);
        if (merchantMatcher.find()) {
            return merchantMatcher.group(1).trim();
        }
        return "未知";
    }
}
