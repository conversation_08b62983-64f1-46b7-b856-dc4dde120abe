package com.cht.autowallet.parser;

import android.util.Log;

import com.cht.autowallet.data.Transaction;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parser for WeChat Pay notifications
 * Handles various WeChat Pay notification formats for payments and receipts
 */
public class WeChatPayParser implements NotificationParser {
    
    private static final String TAG = "WeChatPayParser";
    private static final String WECHAT_PACKAGE = "com.tencent.mm";
    
    // Regex patterns for different WeChat Pay notification formats
    private static final Pattern PAYMENT_PATTERN = Pattern.compile(
        ".*?微信支付.*?向(.+?)付款([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern RECEIPT_PATTERN = Pattern.compile(
        ".*?微信支付.*?收到(.+?)转账([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TRANSFER_PATTERN = Pattern.compile(
        ".*?微信转账.*?([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern RED_PACKET_PATTERN = Pattern.compile(
        ".*?红包.*?([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    // Alternative patterns - support both ¥ and 元
    private static final Pattern AMOUNT_PATTERN = Pattern.compile(
        "([0-9]+\\.?[0-9]*)[元¥]", Pattern.CASE_INSENSITIVE
    );

    // Pattern for "已支付" format
    private static final Pattern PAID_PATTERN = Pattern.compile(
        ".*?已支付[¥￥]?([0-9]+\\.?[0-9]*).*", Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public boolean canHandle(String packageName) {
        return WECHAT_PACKAGE.equals(packageName);
    }
    
    @Override
    public Transaction parseNotification(String packageName, String notificationText, long timestamp) {
        if (!canHandle(packageName)) {
            return null;
        }
        
        Log.d(TAG, "Parsing WeChat notification: " + notificationText);
        
        // Skip non-payment notifications
        if (!isPaymentNotification(notificationText)) {
            return null;
        }
        
        // Try different patterns
        Transaction transaction = tryParsePayment(notificationText, timestamp);
        if (transaction == null) {
            transaction = tryParseReceipt(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseTransfer(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseRedPacket(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParsePaid(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseGeneric(notificationText, timestamp);
        }
        
        if (transaction != null) {
            transaction.setAppName("微信支付");
            transaction.setAppPackage(packageName);
            transaction.setCurrency("CNY");
            transaction.setRawNotificationText(notificationText);
        }
        
        return transaction;
    }
    
    private boolean isPaymentNotification(String text) {
        return text.contains("微信支付") || text.contains("微信转账") ||
               text.contains("红包") || text.contains("付款") ||
               text.contains("收款") || text.contains("转账") ||
               text.contains("已支付") || text.contains("支付成功");
    }
    
    private Transaction tryParsePayment(String text, long timestamp) {
        Matcher matcher = PAYMENT_PATTERN.matcher(text);
        if (matcher.find()) {
            String merchant = matcher.group(1);
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                return new Transaction(
                    "微信支付", WECHAT_PACKAGE, "expense", amount, "CNY",
                    merchant, "微信支付", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseReceipt(String text, long timestamp) {
        Matcher matcher = RECEIPT_PATTERN.matcher(text);
        if (matcher.find()) {
            String payer = matcher.group(1);
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                return new Transaction(
                    "微信支付", WECHAT_PACKAGE, "income", amount, "CNY",
                    payer, "微信收款", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseTransfer(String text, long timestamp) {
        Matcher matcher = TRANSFER_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(1);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                // Determine if it's incoming or outgoing based on keywords
                String type = text.contains("收到") ? "income" : "expense";
                String description = type.equals("income") ? "微信转账收款" : "微信转账";
                
                return new Transaction(
                    "微信支付", WECHAT_PACKAGE, type, amount, "CNY",
                    "未知", description, timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseRedPacket(String text, long timestamp) {
        Matcher matcher = RED_PACKET_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(1);

            try {
                double amount = Double.parseDouble(amountStr);

                // Red packets are usually income when received
                String type = text.contains("收到") || text.contains("领取") ? "income" : "expense";
                String description = type.equals("income") ? "微信红包收入" : "微信红包支出";

                return new Transaction(
                    "微信支付", WECHAT_PACKAGE, type, amount, "CNY",
                    "红包", description, timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }

    private Transaction tryParsePaid(String text, long timestamp) {
        Matcher matcher = PAID_PATTERN.matcher(text);
        if (matcher.find()) {
            String amountStr = matcher.group(1);

            try {
                double amount = Double.parseDouble(amountStr);

                return new Transaction(
                    "微信支付", WECHAT_PACKAGE, "expense", amount, "CNY",
                    "未知", "微信支付", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseGeneric(String text, long timestamp) {
        // Try to extract amount and determine type based on keywords
        Matcher amountMatcher = AMOUNT_PATTERN.matcher(text);
        if (amountMatcher.find()) {
            String amountStr = amountMatcher.group(1);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                // Determine transaction type based on keywords
                String type = "expense"; // Default to expense
                String description = "微信交易";

                if (text.contains("收到") || text.contains("收款") || text.contains("领取")) {
                    type = "income";
                    description = "微信收款";
                } else if (text.contains("已支付") || text.contains("支付成功") || text.contains("付款")) {
                    type = "expense";
                    description = "微信支付";
                }
                
                return new Transaction(
                    "微信支付", WECHAT_PACKAGE, type, amount, "CNY",
                    "未知", description, timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        
        return null;
    }
}
