package com.cht.autowallet.parser;

import java.util.ArrayList;
import java.util.List;

/**
 * Factory class to get appropriate notification parser for different apps
 */
public class NotificationParserFactory {
    
    private List<NotificationParser> parsers;
    
    public NotificationParserFactory() {
        initializeParsers();
    }
    
    private void initializeParsers() {
        parsers = new ArrayList<>();
        parsers.add(new AlipayParser());
        parsers.add(new WeChatPayParser());
        parsers.add(new BankParser());
    }
    
    /**
     * Get the appropriate parser for the given package name
     * 
     * @param packageName The package name of the app
     * @return NotificationParser that can handle the package, or null if none found
     */
    public NotificationParser getParser(String packageName) {
        for (NotificationParser parser : parsers) {
            if (parser.canHandle(packageName)) {
                return parser;
            }
        }
        return null;
    }
    
    /**
     * Get all available parsers
     * 
     * @return List of all notification parsers
     */
    public List<NotificationParser> getAllParsers() {
        return new ArrayList<>(parsers);
    }
}
