package com.cht.autowallet.parser;

import com.cht.autowallet.data.Transaction;

/**
 * Interface for parsing payment notifications from different apps
 */
public interface NotificationParser {
    
    /**
     * Parse a notification text and extract transaction information
     * 
     * @param packageName The package name of the app that sent the notification
     * @param notificationText The full notification text (title + content)
     * @param timestamp The timestamp when the notification was received
     * @return Transaction object if parsing successful, null otherwise
     */
    Transaction parseNotification(String packageName, String notificationText, long timestamp);
    
    /**
     * Check if this parser can handle notifications from the given package
     * 
     * @param packageName The package name to check
     * @return true if this parser can handle the package, false otherwise
     */
    boolean canHandle(String packageName);
}
