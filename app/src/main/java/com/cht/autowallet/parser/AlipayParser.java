package com.cht.autowallet.parser;

import android.util.Log;

import com.cht.autowallet.data.Transaction;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parser for Alipay notifications
 * Handles various Alipay notification formats for payments and receipts
 */
public class AlipayParser implements NotificationParser {
    
    private static final String TAG = "AlipayParser";
    private static final String ALIPAY_PACKAGE = "com.eg.android.AlipayGphone";
    
    // Regex patterns for different Alipay notification formats
    private static final Pattern PAYMENT_PATTERN = Pattern.compile(
        ".*?支付给(.+?)([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern RECEIPT_PATTERN = Pattern.compile(
        ".*?收到(.+?)付款([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TRANSFER_OUT_PATTERN = Pattern.compile(
        ".*?转账给(.+?)([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TRANSFER_IN_PATTERN = Pattern.compile(
        ".*?(.+?)转账给您([0-9]+\\.?[0-9]*)元.*", Pattern.CASE_INSENSITIVE
    );
    
    // Alternative patterns for different notification formats - support both ¥ and 元
    private static final Pattern AMOUNT_PATTERN = Pattern.compile(
        "([0-9]+\\.?[0-9]*)[元¥]", Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public boolean canHandle(String packageName) {
        return ALIPAY_PACKAGE.equals(packageName);
    }
    
    @Override
    public Transaction parseNotification(String packageName, String notificationText, long timestamp) {
        if (!canHandle(packageName)) {
            return null;
        }
        
        Log.d(TAG, "Parsing Alipay notification: " + notificationText);
        
        // Try different patterns
        Transaction transaction = tryParsePayment(notificationText, timestamp);
        if (transaction == null) {
            transaction = tryParseReceipt(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseTransferOut(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseTransferIn(notificationText, timestamp);
        }
        if (transaction == null) {
            transaction = tryParseGeneric(notificationText, timestamp);
        }
        
        if (transaction != null) {
            transaction.setAppName("支付宝");
            transaction.setAppPackage(packageName);
            transaction.setCurrency("CNY");
            transaction.setRawNotificationText(notificationText);
        }
        
        return transaction;
    }
    
    private Transaction tryParsePayment(String text, long timestamp) {
        Matcher matcher = PAYMENT_PATTERN.matcher(text);
        if (matcher.find()) {
            String merchant = matcher.group(1);
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                return new Transaction(
                    "支付宝", ALIPAY_PACKAGE, "expense", amount, "CNY",
                    merchant, "支付宝支付", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseReceipt(String text, long timestamp) {
        Matcher matcher = RECEIPT_PATTERN.matcher(text);
        if (matcher.find()) {
            String payer = matcher.group(1);
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                return new Transaction(
                    "支付宝", ALIPAY_PACKAGE, "income", amount, "CNY",
                    payer, "支付宝收款", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseTransferOut(String text, long timestamp) {
        Matcher matcher = TRANSFER_OUT_PATTERN.matcher(text);
        if (matcher.find()) {
            String recipient = matcher.group(1);
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                return new Transaction(
                    "支付宝", ALIPAY_PACKAGE, "expense", amount, "CNY",
                    recipient, "支付宝转账", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseTransferIn(String text, long timestamp) {
        Matcher matcher = TRANSFER_IN_PATTERN.matcher(text);
        if (matcher.find()) {
            String sender = matcher.group(1);
            String amountStr = matcher.group(2);
            
            try {
                double amount = Double.parseDouble(amountStr);
                return new Transaction(
                    "支付宝", ALIPAY_PACKAGE, "income", amount, "CNY",
                    sender, "支付宝转账", timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        return null;
    }
    
    private Transaction tryParseGeneric(String text, long timestamp) {
        // Try to extract amount and determine type based on keywords
        Matcher amountMatcher = AMOUNT_PATTERN.matcher(text);
        if (amountMatcher.find()) {
            String amountStr = amountMatcher.group(1);
            
            try {
                double amount = Double.parseDouble(amountStr);
                
                // Determine transaction type based on keywords
                String type = "expense"; // Default to expense
                String description = "支付宝交易";
                
                if (text.contains("收到") || text.contains("收款") || text.contains("转账给您")) {
                    type = "income";
                    description = "支付宝收款";
                }
                
                return new Transaction(
                    "支付宝", ALIPAY_PACKAGE, type, amount, "CNY",
                    "未知", description, timestamp, text
                );
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to parse amount: " + amountStr);
            }
        }
        
        return null;
    }
}
