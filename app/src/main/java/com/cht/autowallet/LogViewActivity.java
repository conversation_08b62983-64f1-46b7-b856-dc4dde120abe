package com.cht.autowallet;

import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.cht.autowallet.adapter.NotificationLogAdapter;
import com.cht.autowallet.data.NotificationLogRepository;
import com.cht.autowallet.viewmodel.NotificationLogViewModel;

import java.util.ArrayList;

/**
 * 通知拦截日志查看界面
 */
public class LogViewActivity extends AppCompatActivity {
    
    private TextView statusText;
    private RecyclerView logRecyclerView;
    private NotificationLogAdapter logAdapter;
    private NotificationLogViewModel logViewModel;
    private NotificationLogRepository logRepository;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_log_view);
        
        // 设置标题栏
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("通知拦截日志");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        initViews();
        setupRecyclerView();
        setupViewModel();
        observeData();
    }
    
    private void initViews() {
        statusText = findViewById(R.id.statusText);
        logRecyclerView = findViewById(R.id.logRecyclerView);
    }
    
    private void setupRecyclerView() {
        logAdapter = new NotificationLogAdapter(new ArrayList<>());
        logRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        logRecyclerView.setAdapter(logAdapter);
    }
    
    private void setupViewModel() {
        logViewModel = new ViewModelProvider(this).get(NotificationLogViewModel.class);
        logRepository = new NotificationLogRepository(this);
    }
    
    private void observeData() {
        // 观察所有日志
        logViewModel.getAllLogs().observe(this, logs -> {
            if (logs != null) {
                logAdapter.updateLogs(logs);
                updateStatusText(logs.size());
            }
        });
        
        // 观察解析成功的日志数量
        logViewModel.getSuccessfulParseCount().observe(this, count -> {
            // 可以在这里更新成功解析的统计信息
        });
    }
    
    private void updateStatusText(int totalCount) {
        statusText.setText("共拦截 " + totalCount + " 条通知");
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.log_view_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_clear_logs) {
            showClearLogsDialog();
            return true;
        } else if (id == R.id.action_filter_success) {
            filterSuccessfulParsing();
            return true;
        } else if (id == R.id.action_filter_all) {
            showAllLogs();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private void showClearLogsDialog() {
        new AlertDialog.Builder(this)
                .setTitle("清除日志")
                .setMessage("确定要清除所有通知拦截日志吗？此操作不可撤销。")
                .setPositiveButton("确定", (dialog, which) -> {
                    logRepository.deleteAllLogs();
                    Toast.makeText(this, "日志已清除", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void filterSuccessfulParsing() {
        logViewModel.getLogsByParseStatus(true).observe(this, logs -> {
            if (logs != null) {
                logAdapter.updateLogs(logs);
                statusText.setText("成功解析 " + logs.size() + " 条通知");
            }
        });
    }
    
    private void showAllLogs() {
        logViewModel.getAllLogs().observe(this, logs -> {
            if (logs != null) {
                logAdapter.updateLogs(logs);
                updateStatusText(logs.size());
            }
        });
    }
}
