package com.cht.autowallet.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.cht.autowallet.R;
import com.cht.autowallet.data.NotificationLog;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 通知日志列表适配器
 */
public class NotificationLogAdapter extends RecyclerView.Adapter<NotificationLogAdapter.LogViewHolder> {
    
    private List<NotificationLog> logs;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm:ss", Locale.getDefault());
    
    public NotificationLogAdapter(List<NotificationLog> logs) {
        this.logs = logs;
    }
    
    @NonNull
    @Override
    public LogViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_notification_log, parent, false);
        return new LogViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull LogViewHolder holder, int position) {
        NotificationLog log = logs.get(position);
        holder.bind(log);
    }
    
    @Override
    public int getItemCount() {
        return logs.size();
    }
    
    public void updateLogs(List<NotificationLog> newLogs) {
        this.logs = newLogs;
        notifyDataSetChanged();
    }
    
    class LogViewHolder extends RecyclerView.ViewHolder {
        private TextView appNameText;
        private TextView titleText;
        private TextView contentText;
        private TextView timeText;
        private TextView parseStatusText;
        private TextView parseResultText;
        
        public LogViewHolder(@NonNull View itemView) {
            super(itemView);
            appNameText = itemView.findViewById(R.id.appNameText);
            titleText = itemView.findViewById(R.id.titleText);
            contentText = itemView.findViewById(R.id.contentText);
            timeText = itemView.findViewById(R.id.timeText);
            parseStatusText = itemView.findViewById(R.id.parseStatusText);
            parseResultText = itemView.findViewById(R.id.parseResultText);
        }
        
        public void bind(NotificationLog log) {
            appNameText.setText(log.getAppName());
            titleText.setText(log.getTitle() != null && !log.getTitle().isEmpty() ? 
                             log.getTitle() : "无标题");
            
            // 显示通知内容，限制长度
            String content = log.getContent();
            if (content != null && content.length() > 100) {
                content = content.substring(0, 100) + "...";
            }
            contentText.setText(content != null && !content.isEmpty() ? content : "无内容");
            
            timeText.setText(dateFormat.format(new Date(log.getTimestamp())));
            
            // 设置解析状态
            if (log.isParseSuccess()) {
                parseStatusText.setText("✓ 解析成功");
                parseStatusText.setTextColor(itemView.getContext().getColor(android.R.color.holo_green_dark));
            } else {
                parseStatusText.setText("✗ 解析失败");
                parseStatusText.setTextColor(itemView.getContext().getColor(android.R.color.holo_red_dark));
            }
            
            parseResultText.setText(log.getParseResult());
            
            // 点击显示完整内容
            itemView.setOnClickListener(v -> {
                // TODO: 显示详细信息对话框
            });
        }
    }
}
