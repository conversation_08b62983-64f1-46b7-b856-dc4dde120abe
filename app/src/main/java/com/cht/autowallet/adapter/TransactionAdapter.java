package com.cht.autowallet.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.cht.autowallet.R;
import com.cht.autowallet.data.Transaction;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for displaying transactions in RecyclerView
 */
public class TransactionAdapter extends RecyclerView.Adapter<TransactionAdapter.TransactionViewHolder> {
    
    private List<Transaction> transactions;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm", Locale.getDefault());
    
    public TransactionAdapter(List<Transaction> transactions) {
        this.transactions = transactions;
    }
    
    @NonNull
    @Override
    public TransactionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_transaction, parent, false);
        return new TransactionViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TransactionViewHolder holder, int position) {
        Transaction transaction = transactions.get(position);
        holder.bind(transaction);
    }
    
    @Override
    public int getItemCount() {
        return transactions.size();
    }
    
    public void updateTransactions(List<Transaction> newTransactions) {
        this.transactions = newTransactions;
        notifyDataSetChanged();
    }
    
    class TransactionViewHolder extends RecyclerView.ViewHolder {
        private TextView appNameText;
        private TextView amountText;
        private TextView merchantText;
        private TextView timeText;
        private TextView typeText;
        
        public TransactionViewHolder(@NonNull View itemView) {
            super(itemView);
            appNameText = itemView.findViewById(R.id.appNameText);
            amountText = itemView.findViewById(R.id.amountText);
            merchantText = itemView.findViewById(R.id.merchantText);
            timeText = itemView.findViewById(R.id.timeText);
            typeText = itemView.findViewById(R.id.typeText);
        }
        
        public void bind(Transaction transaction) {
            appNameText.setText(transaction.getAppName());
            
            // Format amount with currency
            String amountStr = String.format(Locale.getDefault(), "%.2f %s", 
                    transaction.getAmount(), transaction.getCurrency());
            amountText.setText(amountStr);
            
            // Set amount color based on transaction type
            if ("income".equals(transaction.getTransactionType())) {
                amountText.setTextColor(itemView.getContext().getColor(android.R.color.holo_green_dark));
                typeText.setText("收入");
                typeText.setTextColor(itemView.getContext().getColor(android.R.color.holo_green_dark));
            } else {
                amountText.setTextColor(itemView.getContext().getColor(android.R.color.holo_red_dark));
                typeText.setText("支出");
                typeText.setTextColor(itemView.getContext().getColor(android.R.color.holo_red_dark));
            }
            
            merchantText.setText(transaction.getMerchant());
            timeText.setText(dateFormat.format(new Date(transaction.getTimestamp())));
            
            // Set click listener to show details
            itemView.setOnClickListener(v -> {
                // TODO: Show transaction details dialog
            });
        }
    }
}
