package com.cht.autowallet.utils;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.TextUtils;

/**
 * Utility class for managing notification access permissions
 */
public class PermissionManager {
    
    private static final String NOTIFICATION_LISTENER_SETTINGS = "android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS";
    
    /**
     * Check if notification access permission is granted
     * 
     * @param context Application context
     * @return true if permission is granted, false otherwise
     */
    public static boolean isNotificationAccessGranted(Context context) {
        ComponentName cn = new ComponentName(context, "com.cht.autowallet.service.NotificationListenerService");
        String flat = Settings.Secure.getString(context.getContentResolver(), "enabled_notification_listeners");
        return !TextUtils.isEmpty(flat) && flat.contains(cn.flattenToString());
    }
    
    /**
     * Open notification access settings page
     * 
     * @param context Application context
     */
    public static void openNotificationAccessSettings(Context context) {
        Intent intent = new Intent(NOTIFICATION_LISTENER_SETTINGS);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    /**
     * Get the package name of the notification listener service
     *
     * @param context Application context
     * @return Package name string
     */
    public static String getNotificationListenerPackageName(Context context) {
        return context.getPackageName() + "/com.cht.autowallet.service.NotificationListenerService";
    }

    /**
     * Check if battery optimization is ignored for this app
     *
     * @param context Application context
     * @return true if battery optimization is ignored, false otherwise
     */
    public static boolean isBatteryOptimizationIgnored(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            return powerManager != null && powerManager.isIgnoringBatteryOptimizations(context.getPackageName());
        }
        return true; // No battery optimization on older versions
    }

    /**
     * Request to ignore battery optimization
     *
     * @param context Application context
     */
    public static void requestIgnoreBatteryOptimization(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !isBatteryOptimizationIgnored(context)) {
            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + context.getPackageName()));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        }
    }

    /**
     * Open battery optimization settings page
     *
     * @param context Application context
     */
    public static void openBatteryOptimizationSettings(Context context) {
        Intent intent = new Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    /**
     * Check if all required permissions are granted
     *
     * @param context Application context
     * @return true if all permissions are granted, false otherwise
     */
    public static boolean areAllPermissionsGranted(Context context) {
        return isNotificationAccessGranted(context) && isBatteryOptimizationIgnored(context);
    }
}
