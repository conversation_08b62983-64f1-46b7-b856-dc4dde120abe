package com.cht.autowallet.utils;

import com.cht.autowallet.data.NotificationLog;
import com.cht.autowallet.data.NotificationLogRepository;
import com.cht.autowallet.data.Transaction;
import com.cht.autowallet.data.TransactionRepository;

/**
 * Utility class for generating test transaction data
 */
public class TestDataGenerator {
    
    public static void generateSampleTransactions(TransactionRepository repository) {
        long currentTime = System.currentTimeMillis();
        
        // Sample Alipay transactions
        Transaction alipayPayment = new Transaction(
            "支付宝", "com.eg.android.AlipayGphone", "expense", 
            25.80, "CNY", "星巴克", "支付宝支付", 
            currentTime - 3600000, // 1 hour ago
            "支付宝 向星巴克付款25.80元"
        );
        
        Transaction alipayIncome = new Transaction(
            "支付宝", "com.eg.android.AlipayGphone", "income", 
            100.00, "CNY", "张三", "支付宝收款", 
            currentTime - 7200000, // 2 hours ago
            "支付宝 收到张三转账100.00元"
        );
        
        // Sample WeChat transactions
        Transaction wechatPayment = new Transaction(
            "微信支付", "com.tencent.mm", "expense", 
            15.50, "CNY", "麦当劳", "微信支付", 
            currentTime - 1800000, // 30 minutes ago
            "微信支付 向麦当劳付款15.50元"
        );
        
        Transaction wechatIncome = new Transaction(
            "微信支付", "com.tencent.mm", "income", 
            50.00, "CNY", "李四", "微信转账收款", 
            currentTime - 5400000, // 1.5 hours ago
            "微信支付 收到李四转账50.00元"
        );
        
        // Sample bank transaction
        Transaction bankPayment = new Transaction(
            "工商银行", "com.icbc", "expense", 
            200.00, "CNY", "超市", "银行卡支出", 
            currentTime - ********, // 3 hours ago
            "工商银行 在超市消费200.00元"
        );
        
        // Insert sample transactions
        repository.insert(alipayPayment);
        repository.insert(alipayIncome);
        repository.insert(wechatPayment);
        repository.insert(wechatIncome);
        repository.insert(bankPayment);
    }

    public static void generateSampleNotificationLogs(NotificationLogRepository logRepository) {
        long currentTime = System.currentTimeMillis();

        // 成功解析的通知日志
        NotificationLog alipaySuccessLog = new NotificationLog(
            "com.eg.android.AlipayGphone", "支付宝",
            "支付宝", "向星巴克付款25.80元",
            "支付宝 向星巴克付款25.80元，余额1234.56元",
            currentTime - 3600000, true, "解析成功 - expense 25.8元"
        );

        NotificationLog wechatSuccessLog = new NotificationLog(
            "com.tencent.mm", "微信",
            "微信支付", "向麦当劳付款15.50元",
            "微信支付 向麦当劳付款15.50元",
            currentTime - 1800000, true, "解析成功 - expense 15.5元"
        );

        // 测试"已支付¥"格式
        NotificationLog wechatPaidLog = new NotificationLog(
            "com.tencent.mm", "微信",
            "微信支付", "已支付¥5.50",
            "已支付¥5.50",
            currentTime - 900000, true, "解析成功 - expense 5.5元"
        );

        // 解析失败的通知日志
        NotificationLog alipayFailLog = new NotificationLog(
            "com.eg.android.AlipayGphone", "支付宝",
            "支付宝", "您有一笔新的花呗账单",
            "您有一笔新的花呗账单，请及时还款",
            currentTime - 7200000, false, "解析失败 - 未识别为交易通知"
        );

        NotificationLog wechatFailLog = new NotificationLog(
            "com.tencent.mm", "微信",
            "微信", "张三发来一条消息",
            "张三: 你好，今天有空吗？",
            currentTime - 5400000, false, "解析失败 - 未识别为交易通知"
        );

        NotificationLog bankSuccessLog = new NotificationLog(
            "com.icbc", "工商银行",
            "工商银行", "您的账户发生一笔交易",
            "您的账户在超市消费200.00元，余额5678.90元",
            currentTime - ********, true, "解析成功 - expense 200.0元"
        );

        // 插入示例日志
        logRepository.insert(alipaySuccessLog);
        logRepository.insert(wechatSuccessLog);
        logRepository.insert(wechatPaidLog);
        logRepository.insert(alipayFailLog);
        logRepository.insert(wechatFailLog);
        logRepository.insert(bankSuccessLog);
    }
}
