package com.cht.autowallet.utils;

import android.content.Context;
import android.util.Log;

import com.cht.autowallet.data.Transaction;
import com.cht.autowallet.data.TransactionRepository;
import com.cht.autowallet.parser.NotificationParserFactory;
import com.cht.autowallet.parser.NotificationParser;

/**
 * 解析器测试辅助类
 */
public class ParserTestHelper {
    
    private static final String TAG = "ParserTestHelper";
    
    /**
     * 测试微信支付解析器
     */
    public static void testWeChatPayParser() {
        NotificationParserFactory factory = new NotificationParserFactory();
        NotificationParser parser = factory.getParser("com.tencent.mm");
        
        if (parser == null) {
            Log.e(TAG, "WeChat parser not found!");
            return;
        }
        
        // 测试用例
        String[] testCases = {
            "已支付¥5.50",
            "微信支付 向星巴克付款25.80元",
            "微信支付 收到张三转账100.00元",
            "已支付¥12.30",
            "支付成功¥8.88",
            "微信转账 50.00元",
            "红包 6.66元"
        };
        
        long currentTime = System.currentTimeMillis();
        
        Log.d(TAG, "=== 微信支付解析器测试 ===");
        
        for (String testCase : testCases) {
            Log.d(TAG, "测试输入: " + testCase);
            
            Transaction result = parser.parseNotification("com.tencent.mm", testCase, currentTime);
            
            if (result != null) {
                Log.d(TAG, "✓ 解析成功: " + result.getTransactionType() + " " + 
                      result.getAmount() + " " + result.getCurrency() + 
                      " 商户: " + result.getMerchant());
            } else {
                Log.d(TAG, "✗ 解析失败");
            }
            Log.d(TAG, "---");
        }
        
        Log.d(TAG, "=== 测试完成 ===");
    }
    
    /**
     * 测试支付宝解析器
     */
    public static void testAlipayParser() {
        NotificationParserFactory factory = new NotificationParserFactory();
        NotificationParser parser = factory.getParser("com.eg.android.AlipayGphone");
        
        if (parser == null) {
            Log.e(TAG, "Alipay parser not found!");
            return;
        }
        
        // 测试用例
        String[] testCases = {
            "支付宝 向星巴克付款25.80元",
            "支付宝 收到张三付款100.00元",
            "支付宝 转账给李四50.00元",
            "支付宝 张三转账给您30.00元",
            "支付宝 付款¥15.50",
            "支付宝 收款¥88.88"
        };
        
        long currentTime = System.currentTimeMillis();
        
        Log.d(TAG, "=== 支付宝解析器测试 ===");
        
        for (String testCase : testCases) {
            Log.d(TAG, "测试输入: " + testCase);
            
            Transaction result = parser.parseNotification("com.eg.android.AlipayGphone", testCase, currentTime);
            
            if (result != null) {
                Log.d(TAG, "✓ 解析成功: " + result.getTransactionType() + " " + 
                      result.getAmount() + " " + result.getCurrency() + 
                      " 商户: " + result.getMerchant());
            } else {
                Log.d(TAG, "✗ 解析失败");
            }
            Log.d(TAG, "---");
        }
        
        Log.d(TAG, "=== 测试完成 ===");
    }
    
    /**
     * 测试完整的解析和存储流程
     */
    public static void testFullWorkflow(Context context) {
        Log.d(TAG, "=== 完整流程测试 ===");

        TransactionRepository repository = new TransactionRepository(context);
        NotificationParserFactory factory = new NotificationParserFactory();

        // 测试"已支付¥5.50"的完整流程
        String testNotification = "已支付¥5.50";
        String packageName = "com.tencent.mm";

        Log.d(TAG, "测试通知: " + testNotification);
        Log.d(TAG, "来源应用: " + packageName);

        NotificationParser parser = factory.getParser(packageName);
        if (parser != null) {
            Transaction transaction = parser.parseNotification(packageName, testNotification, System.currentTimeMillis());

            if (transaction != null) {
                Log.d(TAG, "✓ 解析成功: " + transaction.toString());

                // 测试数据库插入
                new Thread(() -> {
                    try {
                        long id = repository.insertSync(transaction);
                        transaction.setId(id);
                        Log.d(TAG, "✓ 数据库插入成功，ID: " + id);
                        Log.d(TAG, "✓ 完整流程测试成功");
                    } catch (Exception e) {
                        Log.e(TAG, "✗ 数据库插入失败: " + e.getMessage());
                    }
                }).start();
            } else {
                Log.d(TAG, "✗ 解析失败");
            }
        } else {
            Log.d(TAG, "✗ 找不到解析器");
        }
    }

    /**
     * 运行所有解析器测试
     */
    public static void runAllTests() {
        testWeChatPayParser();
        testAlipayParser();
    }
}
