<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="AutoWallet - 自动记账"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="检查权限状态..."
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/permissionButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="检查权限"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/batteryOptimizationButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="关闭电池优化"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/testDataButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="生成测试数据"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/testParserButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="测试解析器"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/viewLogsButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="查看拦截日志"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="交易记录"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/transactionRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>