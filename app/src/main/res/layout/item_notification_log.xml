<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 头部信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/appNameText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="支付宝"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/parseStatusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓ 解析成功"
                android:textSize="12sp"
                android:background="@drawable/type_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp" />

        </LinearLayout>

        <!-- 通知标题 -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="通知标题"
            android:textSize="14sp"
            android:layout_marginTop="4dp"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 通知内容 -->
        <TextView
            android:id="@+id/contentText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="通知内容"
            android:textSize="13sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="4dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- 解析结果 -->
        <TextView
            android:id="@+id/parseResultText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="解析结果"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="4dp"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 时间 -->
        <TextView
            android:id="@+id/timeText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="12-25 14:30:45"
            android:textSize="11sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="4dp"
            android:gravity="end" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
