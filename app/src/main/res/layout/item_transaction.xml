<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/appNameText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="支付宝"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/typeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="支出"
                    android:textSize="12sp"
                    android:background="@drawable/type_background"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/merchantText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="商户名称"
                android:textSize="16sp"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/timeText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="12-25 14:30"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/amountText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="¥100.00"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
