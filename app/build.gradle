plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.cht.autowallet'
    compileSdk 36

    defaultConfig {
        applicationId "com.cht.autowallet"
        minSdk 34
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.recyclerview
    implementation libs.lifecycle.viewmodel
    implementation libs.lifecycle.livedata
    implementation libs.cardview

    // Room database
    implementation libs.room.runtime
    annotationProcessor libs.room.compiler

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}