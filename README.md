# AutoWallet - 自动记账应用

AutoWallet 是一个基于 Android 通知监听的自动记账应用，能够自动识别支付宝、微信支付和银行应用的交易通知，并自动记录交易信息。

## 功能特性

### 已实现功能 (MVP)

1. **通知监听服务** - 监听目标支付应用的通知
2. **智能解析** - 自动解析通知文本提取交易信息
3. **本地存储** - 使用 Room 数据库存储交易记录
4. **权限管理** - 引导用户授予通知访问权限和电池优化白名单
5. **交易展示** - 简洁的界面显示已记录的交易
6. **后台运行保障** - 防止系统杀死通知监听服务
7. **通知拦截日志** - 记录所有拦截的通知内容和解析状态

### 支持的应用

- **支付宝** (com.eg.android.AlipayGphone)
- **微信支付** (com.tencent.mm)
- **主要银行应用**:
  - 工商银行 (com.icbc)
  - 建设银行 (com.ccb.ccbnetpay)
  - 交通银行 (com.bankcomm.Bankcomm)
  - 招商银行 (cmb.pb)
  - 中国银行 (com.chinamworld.bocmbci)
  - 农业银行 (com.abc.mobile.android)
  - 浦发银行 (com.spdb.mobilebank.per)
  - 中信银行 (com.citic.bank.mobile)
  - 平安银行 (com.pingan.paces.ccms)
  - 招商银行信用卡 (com.cmbchina.ccd.pluto.cmbActivity)

## 技术架构

### 核心组件

1. **NotificationListenerService** - 系统级通知监听服务
2. **通知解析器** - 针对不同应用的文本解析逻辑
3. **Room 数据库** - 本地数据存储（交易记录 + 通知日志）
4. **MVVM 架构** - ViewModel + LiveData 数据绑定
5. **权限管理系统** - 通知访问权限 + 电池优化白名单
6. **日志查看界面** - 调试和验证通知拦截效果

### 项目结构

```
app/src/main/java/com/cht/autowallet/
├── data/                          # 数据层
│   ├── Transaction.java           # 交易实体
│   ├── TransactionDao.java        # 数据访问对象
│   ├── AutoWalletDatabase.java    # Room 数据库
│   └── TransactionRepository.java # 数据仓库
├── service/                       # 服务层
│   └── NotificationListenerService.java # 通知监听服务
├── parser/                        # 解析器
│   ├── NotificationParser.java    # 解析器接口
│   ├── AlipayParser.java          # 支付宝解析器
│   ├── WeChatPayParser.java       # 微信支付解析器
│   ├── BankParser.java            # 银行解析器
│   └── NotificationParserFactory.java # 解析器工厂
├── adapter/                       # UI 适配器
│   └── TransactionAdapter.java    # 交易列表适配器
├── viewmodel/                     # 视图模型
│   └── TransactionViewModel.java  # 交易视图模型
├── utils/                         # 工具类
│   ├── PermissionManager.java     # 权限管理
│   └── TestDataGenerator.java     # 测试数据生成
└── MainActivity.java              # 主界面
```

## 使用说明

### 安装和设置

1. **安装应用** - 编译并安装 APK 到 Android 设备
2. **授予通知权限** - 点击"授予通知权限"按钮，在系统设置中开启通知访问权限
3. **关闭电池优化** - 点击"关闭电池优化"按钮，将应用加入电池优化白名单
4. **开始使用** - 权限授予后，应用将自动在后台监听支付通知

### 测试功能

1. **生成测试数据** - 点击"生成测试数据"按钮查看示例交易记录和通知日志
2. **查看拦截日志** - 点击"查看拦截日志"按钮查看所有被拦截的通知内容
3. **实际测试** - 使用支付宝、微信支付或银行应用进行交易，观察是否自动记录
4. **日志验证** - 在日志界面查看通知是否成功拦截和解析

### 权限要求

- **通知访问权限** - 必需，用于监听支付应用通知
- **电池优化白名单** - 推荐，防止系统杀死后台服务
- **前台服务权限** - 自动授予，用于保持后台运行
- **唤醒锁权限** - 自动授予，防止设备休眠时停止服务

## 技术实现细节

### 通知解析逻辑

每个支付应用都有专门的解析器，使用正则表达式匹配通知文本：

- **支付宝**: 识别"支付给"、"收到"、"转账"等关键词，支持"元"和"¥"符号
- **微信支付**: 识别"微信支付"、"微信转账"、"红包"、"已支付"等，支持"元"和"¥"符号
- **银行**: 识别"支出"、"收入"、"转账"等通用银行术语，支持"元"和"¥"符号

**支持的通知格式示例**：
- 微信支付: "已支付¥5.50"、"微信支付 向商户付款25.80元"
- 支付宝: "支付宝 向星巴克付款25.80元"、"收到张三付款100.00元"
- 银行: "您的账户在超市消费200.00元"

### 数据存储

使用 Room 数据库存储两类数据：

**交易记录表 (transactions)**：
- 应用名称和包名
- 交易类型（收入/支出）
- 金额和货币
- 商户信息
- 时间戳
- 原始通知文本（用于调试）

**通知日志表 (notification_logs)**：
- 通知来源应用信息
- 通知标题、内容和大文本
- 拦截时间戳
- 解析是否成功
- 解析结果描述

### 安全考虑

- 所有数据仅存储在本地设备
- 不上传任何交易信息到服务器
- 通知访问权限仅用于读取支付相关通知
- 后台运行权限仅用于保持通知监听服务
- 日志功能主要用于开发调试，可随时清除

## 开发和调试

### 构建项目

```bash
./gradlew build
```

### 运行测试

```bash
./gradlew test
```

### 调试日志

使用 `AutoWallet_NLS` 标签查看通知监听日志：

```bash
adb logcat -s AutoWallet_NLS
```

## 新增功能详解

### 后台运行保障

为确保通知监听服务能够持续运行，应用实现了以下机制：

1. **前台服务权限** - 允许应用在后台运行服务
2. **唤醒锁权限** - 防止设备休眠时停止服务
3. **电池优化白名单** - 防止系统自动杀死应用进程
4. **权限引导界面** - 帮助用户正确配置所需权限

### 通知拦截日志系统

为了验证通知拦截的技术可行性，应用提供了完整的日志记录功能：

**日志记录内容**：
- 所有目标应用的通知内容（标题、正文、大文本）
- 通知拦截的精确时间
- 解析是否成功及详细结果
- 应用来源信息

**日志查看功能**：
- 按时间倒序显示所有拦截的通知
- 区分解析成功和失败的通知
- 支持筛选显示（全部/仅成功解析）
- 一键清除所有日志记录

**调试价值**：
- 验证目标应用的通知是否被成功拦截
- 分析解析失败的原因，优化解析逻辑
- 监控应用在不同场景下的工作状态
- 为后续功能开发提供数据支持

## 已知限制

1. **通知格式变化** - 支付应用更新可能改变通知格式，需要更新解析逻辑
2. **权限依赖** - 需要用户手动授予通知访问权限
3. **解析准确性** - 复杂的通知文本可能解析失败
4. **系统兼容性** - 不同 Android 版本的通知机制可能有差异

## 后续开发计划

1. **增强解析器** - 支持更多银行和支付应用
2. **分类管理** - 自动分类交易（餐饮、交通、购物等）
3. **统计分析** - 月度/年度支出统计和图表
4. **数据导出** - 支持导出 CSV/Excel 格式
5. **云端同步** - 可选的云端备份功能

## 许可证

本项目仅用于技术验证和学习目的。请遵守相关法律法规，不要用于商业用途。
